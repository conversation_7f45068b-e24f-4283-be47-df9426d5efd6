import request from '@/utils/request';

//新增
export const addExperiment = data => {
  return request({
    url: '/testSheet',
    method: 'post',
    data,
  });
};

//列表
export const getExperimentList = params => {
  return request({
    url: '/testSheet/list',
    method: 'get',
    params,
  });
};

//列表
export const getTestSheetList = params => {
  return request({
    url: '/testSheet/projectList',
    method: 'get',
    params,
  });
};

//详情
export const getExperimentDetail = id => {
  return request({
    url: '/testSheet/' + id,
    method: 'get',
  });
};

//编辑
export const putExperiment = data => {
  return request({
    url: '/testSheet',
    method: 'put',
    data,
  });
};

//删除
export const delExperiment = data => {
  return request({
    url: '/testSheet/remove',
    method: 'delete',
    data,
  });
};
