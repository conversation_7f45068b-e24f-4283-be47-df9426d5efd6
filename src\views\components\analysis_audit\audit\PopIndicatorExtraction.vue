<template>
<el-dialog :append-to-body="true" v-model="open" width="60%" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close" class="pop-container">
  <div class="f-18 f-bold c-33 mb10">{{standardItem.name}}</div>
  <el-tabs class="workbench-tabs" v-model="activeTab" @tab-click="handleClick">
    <el-tab-pane label="全文提取" name="all">
      <all-content v-show="activeTab=='all'" :id="standardItem.id" />
    </el-tab-pane>
    <el-tab-pane label="指标项提取" name="indicator">
      <indicator-content v-show="activeTab == 'indicator'" :id="standardItem.id" />
    </el-tab-pane>
  </el-tabs>
</el-dialog>
</template>
<script setup>  
import AllContent from '@/views/components/analysis_audit/audit/AllContent';
import IndicatorContent from '@/views/components/analysis_audit/audit/IndicatorContent';

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  standardItem: {
    type: Object,
    default: {}
  }
});
const { open,standardItem } = toRefs(props)

const title = ref('指标提取')
const activeTab = ref('all')
const finishLoading = ref(false)
const form = ref({
  standardId: undefined,
  analysisStatus: undefined
})
const rules = ref({
  analysisStatus: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
});
onMounted(()=>{
  form.value.standardId = standardItem.value.standardId
})

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const handleClick = (tab) => {
  
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs__item) {
    font-size: 16px !important;
  }
:deep(.el-tabs__item.is-active) {
  font-size: 16px !important;
  color: $primary-color;
  font-weight: bold;
}
</style>