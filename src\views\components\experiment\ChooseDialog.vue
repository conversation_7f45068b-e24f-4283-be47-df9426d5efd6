<template>
  <el-dialog
    width="1100px"
    title="添加试验项目"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      :border="true"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column type="selection" />
      <el-table-column label="序号" type="index" width="70" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="检验项目" show-overflow-tooltip min-width="180" />
      <el-table-column prop="testMethod" label="试验方法" show-overflow-tooltip min-width="180" />
      <el-table-column prop="testDemand" label="试验要求" show-overflow-tooltip min-width="150" />
      <el-table-column prop="standardId" label="所属标准" show-overflow-tooltip min-width="150" />
    </el-table>
    <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="info" @click="handleClose">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getTestSheetList } from '@/api/experiment';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    selectTable: {
      type: Array,
      default: () => {
        return [];
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const tableRef = ref();
  const tableLoading = ref(false);
  const btnLoading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const selectedRows = ref([]);

  const getList = val => {
    tableLoading.value = true;
    if (val) queryParams.value[val] = 1;
    getTestSheetList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
        proxy.$nextTick(() => {
          if (props.selectTable && props.selectTable.length > 0) {
            tableData.value.forEach(row => {
              if (props.selectTable.some(item => item.id == row.id)) tableRef.value.toggleRowSelection(row, true);
            });
          }
        });
      })
      .finally(() => {
        tableLoading.value = false;
      });
  };

  const handleSelectionChange = selection => {
    selectedRows.value = selection;
  };

  const handleConfirm = () => {
    btnLoading.value = true;
    if (!selectedRows.value.length) {
      proxy.$message.warning('请选择试验项目');
      btnLoading.value = false;
    } else {
      emit('chooseData', selectedRows.value);
      handleClose();
    }
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'chooseData']);

  getList();
</script>

<style lang="scss" scoped></style>
