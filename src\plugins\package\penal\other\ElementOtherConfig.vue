<template>
  <div class="panel-tab__content">
    <div class="element-property input-property">
      <div class="element-property__label">元素文档：</div>
      <div class="element-property__value">
        <el-input
          type="textarea"
          v-model="documentation"
          size="small"
          resize="vertical"
          :autosize="{ minRows: 2, maxRows: 4 }"
          @input="updateDocumentation"
          @blur="updateDocumentation"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="ElementOtherConfig">

const {proxy} = getCurrentInstance()

const props = defineProps({
  id: String
})
const {id} = toRefs(props)
const state = reactive({
  documentation: "",
  bpmnElement: undefined
})
const {documentation} = toRefs(state)

watch(()=>props.id,(val) => {
  if (val && val.length) {
    proxy.$nextTick(() => {
      const documentations = window.bpmnInstances.bpmnElement.businessObject?.documentation;
      state.documentation = documentations && documentations.length ? documentations[0].text : "";
    });
  } else {
    state.documentation = "";
  }
}, { immediate: true })

function updateDocumentation() {
  (state.bpmnElement && state.bpmnElement.id === props.id) || (state.bpmnElement = window.bpmnInstances.elementRegistry.get(props.id));
  const documentation = window.bpmnInstances.bpmnFactory.create("bpmn:Documentation", { text: state.documentation });
  window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
    documentation: [documentation]
  });
}

onUnmounted(() => {
  state.bpmnElement = null;
})

</script>
