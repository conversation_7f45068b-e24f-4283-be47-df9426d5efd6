<template>
  <el-dialog
    v-model="props.visible"
    width="60%"
    title="指标提取"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
    custom-class="dialog-container"
  >
    <div class="container">
      <div class="container-left">
        <div class="f-18 f-bold c-33">标准数据</div>
        <div class="f-14 c-99 mt5">选择需提取指标标准数据资源</div>
        <el-radio-group v-if="stardardData && stardardData.length > 0" v-model="radioValue" class="scroller-bar-style">
          <el-radio v-for="item in stardardData" :key="item.id" :label="item.id">
            <template #default>
              <div class="flex flex-sb flex-ai-center">
                <div class="radio_label">{{ item.name }}</div>
                <el-icon @click.stop="handleDetail(item)" class="radio_icon" color="#333333"><Document /></el-icon>
              </div>
            </template>
          </el-radio>
        </el-radio-group>
        <empty v-else />
      </div>
      <div class="container-line"></div>
      <div class="container-right">
        <div class="flex flex-ai-center">
          <el-button @click="handleClick" type="primary">全文提取</el-button>
          <el-button @click="handleCheck" type="primary">指标项提取</el-button>
          <el-input v-show="isShow" v-model="keyWorld" placeholder="请输入指标关键词，多个时以顿号分隔" class="ml12" />
          <el-button v-show="isShow" @click="handlekeyWorld" type="primary" class="ml12">开始提取</el-button>
        </div>
        <el-divider />
        <div v-loading="loading">
          <div v-if="IndicatorExtractionData && IndicatorExtractionData.length > 0">
            <div class="flex flex-ai-center c-33 f-14 mb20">
              <el-icon class="c-99 f-18 mr5"><InfoFilled /></el-icon>
              提取指标：
              <span class="c-primary">{{ IndicatorExtractionData.length }}</span>
              &nbsp;项
            </div>
            <div class="scroller-bar-style">
              <el-card v-for="(item, index) in IndicatorExtractionData" :key="item.id">
                <div class="h-title">指标项：{{ index + 1 }}</div>
                <div class="card">
                  <div v-for="(childItem, childIndex) in cardList" :key="childIndex" class="card-item">
                    <div class="card-item-title">{{ childItem }}：</div>
                    <div class="card-item-content">{{ item[childItem] }}</div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
          <empty v-else />
        </div>
      </div>
    </div>
    <pop-audit-task v-if="openAudit" v-model:open="openAudit" :standardItem="currentRow" fromState="2" />
  </el-dialog>
</template>

<script setup>
  import { getOcrList, getIndicatorExtraction } from '@/api/analysis_audit/audit';
  import PopAuditTask from '@/views/components/analysis_audit/audit/PopAuditTask';

  const { proxy } = getCurrentInstance();
  const emit = defineEmits(['update:visible']);

  const props = defineProps({
    visible: Boolean,
  });

  const loading = ref(false);
  const stardardData = ref([]);
  const radioValue = ref('');
  const isShow = ref(false);
  const keyWorld = ref('');
  const IndicatorExtractionData = ref([]);
  const cardList = ref([
    '指标名称',
    '指标内容',
    '计量单位',
    '试验方法',
    '标准对象',
    '指标对象',
    '指标对象属性名',
    '指标对象属性值',
    '指标注',
    '指标类型',
  ]);
  const openAudit = ref(false);
  const currentRow = ref({});

  loading.value = true;
  getOcrList({ pageNum: 1, pageSize: 100, analysisStatus: 1, pageIdx: 40 })
    .then(res => {
      stardardData.value = res.rows;
    })
    .finally(() => {
      loading.value = false;
    });

  const handleClick = () => {
    if (radioValue.value) {
      getIndicatorExtractionData(0);
    } else {
      proxy.$modal.msgWarning('请选择需提取指标标准数据资源');
    }
  };

  const handlekeyWorld = () => {
    if (!radioValue.value) {
      proxy.$modal.msgWarning('请选择需提取指标标准数据资源');
      return;
    }
    if (keyWorld.value) {
      getIndicatorExtractionData(1);
    } else {
      proxy.$modal.msgWarning('请输入指标关键词');
    }
  };

  const handleCheck = () => {
    keyWorld.value = '';
    isShow.value = !isShow.value;
  };

  const getIndicatorExtractionData = type => {
    loading.value = true;

    getIndicatorExtraction({ keyword: keyWorld.value, id: radioValue.value, type: type })
      .then(res => {
        let data = res.data;
        data['指标对象属性名'] = data['指标对象属性'] ? data['指标对象属性']['属性名称'] : '无';
        data['指标对象属性值'] = data['指标对象属性'] ? data['指标对象属性']['属性值'] : '无';
        IndicatorExtractionData.value = data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleDetail = row => {
    currentRow.value = row;
    openAudit.value = true;
  };

  const handleClose = () => {
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    justify-content: space-between;
    min-height: 500px !important;

    &-left {
      width: 25%;
      overflow: hidden;
    }

    &-line {
      width: 1px;
      background-color: #999;
      margin: 0 20px;
    }

    &-right {
      width: 75%;
    }
  }

  .card {
    display: flex;
    flex-wrap: wrap;

    &-item {
      width: 50%;
      display: flex;
      margin-top: 15px;
      font-size: 15px;
      line-height: 25px;

      &-title {
        width: 120px;
        color: #999;
        text-align: right;
      }

      &-content {
        flex: 1;
        color: #333;
        text-align: left;
      }
    }
  }

  .scroller-bar-style {
    &::-webkit-scrollbar {
      width: 0 !important;
    }

    max-height: 500px;
    overflow-y: scroll;
  }

  .radio_label {
    width: calc(100% - 34px);
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .radio_icon {
    font-size: 15px;
  }

  :deep(.el-radio-group) {
    display: block !important;
  }

  :deep(.el-radio) {
    width: 100%;
    display: flex !important;
    align-items: center !important;
    margin-right: 0 !important;
    height: auto !important;
    margin-top: 20px !important;
  }

  :deep(.el-radio__label) {
    width: calc(100% - 14px);
  }

  :deep(.el-input) {
    width: 270px !important;
  }

  :deep(.el-divider--horizontal) {
    margin: 15px 0 !important;
  }

  :deep(.el-card) {
    margin-bottom: 20px !important;
  }

  :deep(.el-card__body) {
    padding: 20px !important;
    box-sizing: border-box;
  }
</style>
