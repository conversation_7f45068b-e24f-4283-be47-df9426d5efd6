<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title>标准数字化平台_安徽标信查数据技术有限公司</title>
  <meta name="keywords" content="标准数字化平台，企业标准化管理，信息管理系统，企业定制化系统，安徽标信查，信息系统" />
  <meta name="description" content="标准数字化平台由安徽标信查数据技术有限公司打造，能够实现企业标准化管理更加标准化和科学化。标准数字化平台由标准管理、标准体系、标准同步、标准雷达、标准应用等部分组成。本系统为企业标准化工作提供了强有力的支撑，大大提高了企业的标准化水平，欢迎广大企业预约咨询，演示预约联系电话：400-109-7887！" />
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <script src="/iconfont.js"></script>
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    .flexbox {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      background-color: #6e96ff;
    }

    .flexbox>div {
      width: 100vw;
      height: 100vh;
      -webkit-box-flex: 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      margin: 0;
      position: relative;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      overflow: hidden;
    }

    .circle-loader {
      position: relative;
      width: auto;
      height: auto;
    }

    .circle-loader div {
      height: 10px;
      width: 10px;
      background-color: #fff;
      border-radius: 50%;
      position: absolute;
      -webkit-animation: 0.8s opaque ease-in-out infinite both;
      animation: 0.8s opaque ease-in-out infinite both;
    }

    .circle-loader>div:nth-child(1) {
      top: -25px;
      left: 0;
    }

    .circle-loader>div:nth-child(2) {
      top: -17px;
      left: 17px;
      -webkit-animation-delay: 0.1s;
      animation-delay: 0.1s;
    }

    .circle-loader>div:nth-child(3) {
      top: 0;
      left: 25px;
      -webkit-animation-delay: 0.2s;
      animation-delay: 0.2s;
    }

    .circle-loader>div:nth-child(4) {
      top: 17px;
      left: 17px;
      -webkit-animation-delay: 0.3s;
      animation-delay: 0.3s;
    }

    .circle-loader>div:nth-child(5) {
      top: 25px;
      left: 0;
      -webkit-animation-delay: 0.4s;
      animation-delay: 0.4s;
    }

    .circle-loader>div:nth-child(6) {
      top: 17px;
      left: -17px;
      -webkit-animation-delay: 0.5s;
      animation-delay: 0.5s;
    }

    .circle-loader>div:nth-child(7) {
      top: 0;
      left: -25px;
      -webkit-animation-delay: 0.6s;
      animation-delay: 0.6s;
    }

    .circle-loader>div:nth-child(8) {
      top: -17px;
      left: -17px;
      -webkit-animation-delay: 0.7s;
      animation-delay: 0.7s;
    }

    .flexbox>div .load_title {
      font-family: 'Open Sans';
      color: #FFF;
      font-size: 16px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 57%;
      opacity: 1;
      line-height: 30px;
    }

    @-webkit-keyframes opaque {
      0% {
        opacity: 0.1;
      }

      40% {
        opacity: 1;
      }

      80% {
        opacity: 0.1;
      }

      100% {
        opacity: 0.1;
      }
    }

    @keyframes opaque {
      0% {
        opacity: 0.1;
      }

      40% {
        opacity: 1;
      }

      80% {
        opacity: 0.1;
      }

      100% {
        opacity: 0.1;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="flexbox">
      <div>
        <div class="circle-loader">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
        <div class="load_title">正在努力加载，请稍等......</div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>