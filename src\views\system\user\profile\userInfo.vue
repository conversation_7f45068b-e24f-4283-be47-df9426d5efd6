<template>
   <el-form ref="userRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="真实姓名" prop="nickName">
         <el-input v-model="form.nickName" maxlength="30" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
         <el-input v-model="form.phonenumber" maxlength="11" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
         <el-input v-model="form.email" maxlength="50" />
      </el-form-item>
      <el-form-item label="性别">
         <el-radio-group v-model="form.sex">
            <el-radio label="0">男</el-radio>
            <el-radio label="1">女</el-radio>
         </el-radio-group>
      </el-form-item>
      <el-form-item>
      <el-button type="primary" @click="submit">保存</el-button>
      <!-- <el-button type="danger" @click="close">关闭</el-button> -->
      </el-form-item>
   </el-form>
</template>

<script setup>
import { updateUserProfile } from "@/api/system/user";

const props = defineProps({
  user: {
    type: Object
  }
});

const { proxy } = getCurrentInstance();

const form = ref({})
const rules = ref({
  nickName: [{ required: true, message: "真实姓名不能为空", trigger: "blur" }],
  email: [{ required: false, type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
  phonenumber: [{ required: false, pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }],
});

watch(
  () => props.user,
  (newVal, oldVal) => {
    if (newVal) {
      form.value = {...newVal}
    }
  },
  { deep: true } // 深度监听
);

/** 提交按钮 */
function submit() {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      emit('updateData', form.value)
      // updateUserProfile(props.user).then(response => {
      //   proxy.$modal.msgSuccess("修改成功");
      // });
    }
  });
};
/** 关闭按钮 */
function close() {
  proxy.$tab.closePage();
};

const emit = defineEmits(['updateData']);
</script>
