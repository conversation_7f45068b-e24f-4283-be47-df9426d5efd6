import request from '@/utils/request';

// 列表
export function getOcrList(params) {
  return request({
    url: '/ocr/list',
    method: 'get',
    params,
  });
}

// 解析
export function getAnalysis(params) {
  return request({
    url: '/ocr/analysis',
    method: 'get',
    params,
  });
}

// 重新解析
export function getReanalysis(params) {
  return request({
    url: '/ocr/reanalysis',
    method: 'get',
    params,
  });
}

// 查看
export function getAnalysisDetail(id) {
  return request({
    url: '/ocr/getAnalysisDetail/' + id,
    method: 'get',
  });
}

// 分派
export function setAssign(data) {
  return request({
    url: '/sdc/stdAnalysisStandard/assign',
    method: 'put',
    data,
  });
}
// // 解析详情-全部（包括目次、正文、pdf地址）
// export function getAnalysisDetail(standardId) {
//   return request({
//     url: '/sdc/stdAnalysisStandard/getAnalysisDetail/' + standardId,
//     method: 'get'
//   });
// }
// 章条移动
export function moveCategory(data) {
  return request({
    url: '/sdc/stdAnalysisStandard/move',
    method: 'put',
    data,
  });
}
// 新增章条、内容
export function addSection(data) {
  return request({
    url: '/sdc/stdAnalysisStandard/addSection',
    method: 'post',
    data,
  });
}
// 编辑章条、内容
export function editSection(data) {
  return request({
    url: '/sdc/stdAnalysisStandard/editSection',
    method: 'post',
    data,
  });
}
// 获取指定章条、内容
export function getSection(sectionId) {
  return request({
    url: '/sdc/stdAnalysisStandard/getSection/' + sectionId,
    method: 'get',
  });
}
// 删除章条、内容
export function deleteSection(id) {
  return request({
    url: '/sdc/stdAnalysisStandard/delSection/' + id,
    method: 'delete',
  });
}
// 审核完成
export function auditCompleted(data) {
  return request({
    url: '/sdc/stdAnalysisStandard/auditCompleted',
    method: 'put',
    data,
  });
}
// 二次审核结果
export function auditSecond(data) {
  return request({
    url: '/sdc/stdAnalysisStandard/auditResult',
    method: 'put',
    data,
  });
}
// 重新加工
export function setRebuild(params) {
  return request({
    url: '/sdc/stdAnalysisStandard/restarAftercure',
    method: 'get',
    params,
  });
}
// 提取接口
export function getExtraction(params) {
  return request({
    url: '/ocr/indexExtraction',
    method: 'get',
    params,
  });
}

export function getIndicatorExtraction(data) {
  return request({
    url: 'ocr/informationExtraction',
    method: 'get',
    params: data,
  });
}
