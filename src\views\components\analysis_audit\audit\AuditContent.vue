<template>
<div v-if="auditStore.content" v-html="auditStore.content" style="padding: 10px 10px 0 10px;"></div>
<el-empty v-else />
</template>
<script setup>
import useAuditStandardStore from '@/store/modules/auditStandard'

const auditStore = useAuditStandardStore()

</script>
<style lang="scss">
.bxcjx-content-wrap{
  .c-title{
    font-weight: 600;
    font-size: 16px;
    color: #333333; 
  }

  table {
    border-collapse: collapse
  }

  table,th,td {
    border: 1px solid #999;
    padding: 8px;
  }
  img{
    max-width: 100%;
    height: auto;
  }
}
</style>