<template>
  <div class="login-wrap">
    <div class="login-main-wrap">
      <div class="login-left">
        <div class="login-logo">
          <img src="@/assets/images/login/login_logo.png" alt="">
        </div>
      </div>
      <div class="login-right">
        <div class="login-form-wrap">
          <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
            <div class="welcome">欢迎登录~</div>
            <h3 class="title">标准数字化平台</h3>
              <el-form-item prop="username">
                <el-input
                  v-model="loginForm.username"
                  type="text"
                  size="large"
                  auto-complete="off"
                  placeholder="账号"
                >
                  <template #prefix><svg-icon icon-class="login_user" class="el-input__icon input-icon" /></template>
                </el-input>
              </el-form-item>
              <el-form-item class="mt20" prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  size="large"
                  auto-complete="off"
                  placeholder="密码"
                  show-password
                  @keyup.enter="handleLogin"
                >
                  <template #prefix><svg-icon icon-class="login_pwd" class="el-input__icon input-icon" /></template>
                </el-input>
              </el-form-item>
              <!-- <el-form-item class="verify-wrap mt20" prop="code" v-if="captchaEnabled">
                <el-input
                  v-model="loginForm.code"
                  size="large"
                  auto-complete="off"
                  placeholder="验证码"
                  @keyup.enter="handleLogin"
                >
                  <template #prefix><svg-icon icon-class="login_verify" class="el-input__icon input-icon" /></template>
                </el-input>
                <div class="login-code pointer">
                  <img :src="codeUrl" @click="getCode" class="login-code-img"/>
                </div>
              </el-form-item> -->

              <div class="f-14 m-red mt40 error-wrap">
                {{errorMsg}}
              </div>
              <el-form-item style="width:100%;">
                <el-button
                  :loading="loading"
                  size="large"
                  type="primary"
                  style="width:100%;"
                  @click.prevent="handleLogin"
                >
                  <span v-if="!loading">登 录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
              </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="login-suggestion">建议使用：1920*1080分辨率/谷歌浏览器访问达到最佳效果 丨 版权所有：安徽标信查数据技术有限公司</div>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const PROJECT_PREFIX = 'BXC_SDC_WEB_MANAGE';

const userStore = useUserStore()
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};
// 协议同意状态
const agreement = ref(true)
const codeUrl = ref("");
const loading = ref(false);
const errorMsg = ref("")
// 验证码开关
const captchaEnabled = ref(false);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

function handleLogin() {
  if (!agreement.value){
    proxy.$modal.msgWarning('需要同意《用户协议与隐私政策》')
    return
  }
  proxy.$refs.loginRef.validate((valid,obj) => {
    if (valid) {
      errorMsg.value = ''
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set(PROJECT_PREFIX+"username", loginForm.value.username, { expires: 30 });
        Cookies.set(PROJECT_PREFIX+"password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set(PROJECT_PREFIX+"rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove(PROJECT_PREFIX+"username");
        Cookies.remove(PROJECT_PREFIX+"password");
        Cookies.remove(PROJECT_PREFIX+"rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        router.push({ path: redirect.value || "/" });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }else{
      let arr = [];
      for (let key in obj) {
        arr.push(obj[key][0].message);
      }
      errorMsg.value = arr[0];
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get(PROJECT_PREFIX+"username");
  const password = Cookies.get(PROJECT_PREFIX+"password");
  const rememberMe = Cookies.get(PROJECT_PREFIX+"rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login-wrap {
  height: 100%;
  background-image: url("@/assets/images/login/login_bg.png");
  background-size: cover;
  display:flex;
  justify-content:center;
  align-items:center;
  position: relative;
  min-width: 1200px;
  min-height: 600px;
  .login-main-wrap {
    height: calc(100vh - 100px);
    display:flex;
    .login-left{
      flex: 0 0 50%;
      .login-logo{
        position: fixed;
        top:45px;
        left:50px;
        img{
          height: 69px;
        }
      }
    }
    .login-right{
      flex: 0 0 50%;
      height: 100%;
      box-sizing:border-box;
      margin-left: 150px;
      .login-form-wrap{
        height:100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-image: url("@/assets/images/login/login_box.png");
        background-size: 585px 625px;
        background-repeat: no-repeat;
        background-position:center center;
        width: 585px;
        .login-form{
          display:flex;
          flex-direction: column;
          align-items:center;
          width:440px;
          
          &:deep(.el-input__wrapper) {
            box-shadow: 0 0 0 0px;
            background: #F8F9FC;
            border-radius: 4px;
          }
          .welcome{
            font-size: 24px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 20px;
            align-self: flex-start;
          }
          .title{
            font-size: 32px;
            font-weight: bold;
            color: $primary-color;
            margin: 0;
            padding: 0;
            padding-bottom: 60px;
            align-self: flex-start;
          }
          :deep(.el-form-item) {
            width: 100%;
            height: 56px;
            line-height: 56px;
            border: none;
            margin-right: 0px !important;
            .el-input{
              height: 56px;
              line-height: 56px;
              border: none;
              font-size: 16px;
              
            }
            input:-internal-autofill-previewed,
            input:-internal-autofill-selected {
              -webkit-text-fill-color: #333 !important;
              transition: background-color 5000s ease-in-out 0s !important;
            }
            .el-input__prefix{
              margin-left: 5px;
              width: 30px;
            }
            .el-button{
              height: 56px;
              font-size: 20px;
              background: $primary-color;
              border-radius: 4px;
              font-weight: bold;
            }
            .el-form-item__error{
              display: none;
            }
          }
          .verify-wrap{
            display: flex;
            .el-input{
              flex: 1;
            }
            .login-code{
              margin-left: auto;
              margin-bottom: -15px;
              width: 160px;
              img{
                object-fit: cover;
                width: 160px;
              }
            }
          }
          .privary-wrap{
            align-self: flex-start;
            display: flex;
            align-items: center;
          }      
        }
      }
    }
    .error-wrap{
      height: 20px;
    }
  }
  .login-suggestion {
    color: #333333;
    position: absolute;
    bottom: 10px;
    font-size: 14px;
  }
}
:deep(.el-input .el-input__password){
  font-size: 16px;
  margin-right: 10px;
}
</style>
