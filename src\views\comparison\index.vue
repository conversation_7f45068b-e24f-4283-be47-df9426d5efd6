<template>
    <div class="app-container">
        <div class="comparison">
            <div class="main">
                <el-row :gutter="80">
                    <el-col :md="12" :xs="24">
                        <div class="flex flex-ai-center mb10">
                            <span class="c-F20000">* </span>
                            <span>原标准</span>
                        </div>
                        <div class="flex flex-ai-center flex-jc-between borders" @click="handleOpenDialog(1)">
                            <div class="flex-1 w100 mr10">
                                <div class="overflow-ellipsis" v-if="originalData.length > 0">{{ originalData[0].name }}
                                </div>
                                <div v-else class="c-99">请选择原标准</div>
                            </div>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </div>

                    </el-col>
                    <el-col :md="12" :xs="24">
                        <div class="flex flex-ai-center mb10">
                            <span class="c-F20000">* </span>
                            <span>比对标准</span>
                        </div>
                        <div class="flex flex-ai-center flex-jc-between borders" @click="handleOpenDialog(2)">
                            <div class="flex-1 w100 mr10">
                                <div class="overflow-ellipsis" v-if="compareData.length > 0">{{ compareData[0].name }}
                                </div>
                                <div v-else class="c-99">请选择比对标准</div>
                            </div>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </div>
                    </el-col>
                </el-row>
                <!-- <div class="mt40 nav_bars">
                    <div>比对主题</div>
                    <div class="flex flex-wrap">
                        <div class="item" :class="{ 'active': current_ids.includes('0') }">{{
                            bxc_compare_topic[0]?.label }}</div>
                        <div class="item" :class="{ 'active': current_ids.includes(item.value) }"
                            v-for="item in bxc_compare_topic.slice(1)" :key="item.value"
                            @click="handleClick(item.value)">{{ item.label
                            }}</div>
                    </div>
                </div> -->
                <el-button class="cbtn" type="primary" @click="handleOpenDetailsDialog">开始比对</el-button>
            </div>
        </div>
        <!-- 选择比对标准 -->
        <chooseDialog ref="chooseDialogRef" @chooseData="handleChooseData" />

        <!-- 比对详情 -->
        <detailsDialog ref="detailsDialogRef" />
    </div>
</template>
<script setup>
import { ElLoading } from 'element-plus'
import chooseDialog from '@/views/components/comparison/ChooseDialog.vue'
import detailsDialog from '@/views/components/comparison/DetailsDialog.vue'
import { startCompare } from '@/api/comparison/compare'
const { proxy } = getCurrentInstance();

const { bxc_compare_topic } = proxy.useDict('bxc_compare_topic');
// 比对主题
const nav_bars = ref([
    {
        name: '章节',
        value: 0
    },
    {
        name: '编制单位',
        value: 1
    },
    {
        name: '定义',
        value: 2
    }
])
const current_ids = ref([])
const handleClick = (id) => {
    if (current_ids.value.includes(id)) {
        current_ids.value = current_ids.value.filter(item => item !== id);
    } else {
        current_ids.value.push(id);
    }
}
const type = ref(1) // 1 原标准 2 比对标准
const originalData = ref([]) // 原标准
const compareData = ref([]) // 比对标准

// 选择比对标准
const chooseDialogRef = ref(null)
const handleOpenDialog = (val) => {
    type.value = val
    chooseDialogRef.value.open(val, originalData.value, val == 1 ? originalData.value : compareData.value)
}
const handleChooseData = (data) => {
    if (type.value == 1) {
        originalData.value = data
    } else {
        compareData.value = data
    }
}
// 比对详情
const detailsDialogRef = ref(null)
const handleOpenDetailsDialog = (val) => {
    if (originalData.value.length == 0 || compareData.value.length == 0) {
        proxy.$message.warning('请选择原标准、比对标准');
        return;
    }
    if (originalData.value[0].id == compareData.value[0].id) {
        proxy.$message.warning('原标准和比对标准不能相同');
        return;
    }
    let loading = ElLoading.service({
        lock: true,
        text: '比对中...',
        background: 'rgba(0, 0, 0, 0.7)'
    })
    // 比对数据的名称
    let comparisionInfo = {
        original: originalData.value[0].name,
        compare: compareData.value[0].name
    }
    startCompare({
        sourceFileId: originalData.value[0].id,
        targetFileId: compareData.value[0].id,
        // sourceFileId: 7,
        // targetFileId: 3,
        typeList: current_ids.value
    }).then(res => {
        loading.close();
        detailsDialogRef.value.open(val, res.data, comparisionInfo);
        // 解析公式
        setTimeout(() => {
            MathJax.typesetPromise()
        }, 300);
    }).catch(err => {
        loading.close();
    })
}
</script>
<style scoped lang="scss">
.comparison {
    min-height: calc(100vh - 125px);
    display: flex;
    justify-content: center;
    flex-direction: column;
    background-color: #fff;
    padding-left: 10%;

    .main {
        width: 70%;
        max-width: 1200px;
    }

    .borders {
        border: 1px solid #eaeaea;
        padding: 10px;
        border-radius: 4px;
        cursor: pointer;
    }

    .w100 {
        width: 100px;
    }

    .nav_bars {
        min-height: 120px;

        .item {
            width: 120px;
            line-height: 38px;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            border: 1px solid #eaeaea;
            color: #333;
            margin-right: 20px;
            margin-top: 20px;
        }

        .active {
            background-color: #0272ff;
            color: #fff;
        }
    }

    .cbtn {
        height: 40px;
        max-width: 400px;
        width: 50%;
        min-width: 150px;
        margin: 80px auto;
        display: block;
    }
}
</style>