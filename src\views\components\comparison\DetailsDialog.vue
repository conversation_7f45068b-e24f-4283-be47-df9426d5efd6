<template>
    <el-dialog width="96%" title="标准对比" append-to-body v-model="visible" :close-on-click-modal="false"
        :close-on-press-escape="false" :before-close="handleClose">
        <div v-loading="loading">
            <div class="tbale-main">
                <div class="table-header">
                    <div class="w10">比对主题</div>
                    <div class="w35">{{ comparisionInfo?.original }}</div>
                    <div class="w35">{{ comparisionInfo?.compare }}</div>
                    <div class="w20">差异总结</div>
                </div>
                <el-scrollbar max-height="calc(100vh - 270px)" v-if="dataInfo?.length > 0">
                    <div class="table-tbody" v-for="(item, index) in dataInfo" :key="index">
                        <table class="tables" border="1" cellspacing="0" cellpadding="0"
                            style="width:100%;border-collapse:collapse;">
                            <tr v-for="(row, inde) in item.children" :key="inde">
                                <td class="w10" align="center"
                                    :style="{ borderTop: inde > 0 ? 'none' : '1px solid #f8f8f8' }">
                                    <span v-if="inde == 0">{{ item.name }}</span>
                                </td>
                                <td class="w35">
                                    <div class="text" v-html="row.content1"></div>
                                    <!-- <MarkdownView :content="row.content1" /> -->
                                </td>
                                <td class="w35">
                                    <div class="text" v-html="row.content2"></div>
                                </td>
                                <td class="w20">
                                    <div class="text" v-html="row.differenceSummary"></div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </el-scrollbar>
                <div v-else>
                    <empty />
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
// import MarkdownView from './MarkdownView.vue'
import { processMathText, replaceMathText } from '@/utils/index'
const { proxy } = getCurrentInstance();
const visible = ref(false)
const loading = ref(true);

const type = ref('');
const dataInfo = ref({});
const comparisionInfo = ref({
    original: '',
    compare: ''
})
const open = async (val, data, comparision) => {
    visible.value = true;
    type.value = val;
    comparisionInfo.value = comparision;
    dataInfo.value = data;
    loading.value = false;

}
const handleClose = () => {
    visible.value = false;
};

const parseText = (text) => {
    return replaceMathText(text)
}

defineExpose({
    open,
})

</script>

<style lang="scss">
.tbale-main {
    .w10 {
        width: 10%;
    }

    .w35 {
        width: 35%;
    }

    .w20 {
        width: 20%;
    }
}

.table-header {
    background-color: #0272ff;
    color: #fff;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    border-radius: 4px;
    padding: 0 20px;

    div {
        padding: 15px;
        text-align: center;

        &:first-child {
            border-radius: 4px 0 0 4px;
        }

        &:last-child {
            border-radius: 0 4px 4px 0;
        }
    }
}

.table-tbody {
    width: 100%;
    background-color: #d9ecff;
    margin: 10px 0;
    padding: 15px 20px;
    border-radius: 4px;

    .tables {
        border-collapse: collapse;

        td {
            border: 1px solid #f8f8f8;
            padding: 10px 15px;
        }

        .text {
            line-height: 1.6;

        }

        table {
            border-collapse: collapse;

            td {
                border: 1px solid #dddddd;
                padding: 10px 15px;
            }
        }


    }
}
</style>
