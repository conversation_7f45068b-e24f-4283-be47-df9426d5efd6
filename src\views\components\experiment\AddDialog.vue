<template>
  <div>
    <el-dialog
      width="930px"
      :title="props.id ? '编辑试验单' : '新增试验单'"
      append-to-body
      v-model="props.addDialogVisible"
      :before-close="handleClose"
    >
      <div class="h-title">基础信息</div>
      <el-form
        :inline="true"
        label-width="auto"
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="dialog-form-inline"
      >
        <el-form-item label="试验单编号" prop="testSheetCode">
          <el-input maxlength="50" placeholder="请输入试验单编号" v-model="form.testSheetCode" />
        </el-form-item>
        <el-form-item label="样品名称" prop="sampleName">
          <el-input maxlength="50" placeholder="请输入样品名称" v-model="form.sampleName" />
        </el-form-item>
        <el-form-item label="样品型号" prop="sampleModel">
          <el-input maxlength="50" placeholder="请输入样品型号" v-model="form.sampleModel" />
        </el-form-item>
        <el-form-item label="样品数量" prop="sampleNum">
          <el-input maxlength="50" placeholder="请输入样品数量" v-model="form.sampleNum" />
        </el-form-item>
        <el-form-item label="委托单位" prop="entrustedUnit">
          <el-input maxlength="50" placeholder="请输入委托单位名称" v-model="form.entrustedUnit" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input maxlength="50" placeholder="请输入联系人" v-model="form.contactPerson" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input maxlength="50" placeholder="请输入联系方式" v-model="form.contactPhone" />
        </el-form-item>
        <el-form-item label="委托单位意见" class="one-column" prop="entrustedOpinion">
          <el-input
            v-model="form.entrustedOpinion"
            :rows="5"
            :show-word-limit="true"
            maxlength="300"
            type="textarea"
            placeholder="请输入委托单位意见信息"
          />
        </el-form-item>
      </el-form>
      <div class="flex flex-ai-center flex-sb mt20">
        <div class="h-title">试验项目</div>
        <el-button @click="chooseVisible = true" type="primary" icon="Plus">添加试验项目</el-button>
      </div>
      <el-table :data="form.testProjectList" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" fixed width="60" />
        <el-table-column prop="name" label="检验项目" show-overflow-tooltip min-width="180" fixed />
        <el-table-column prop="testMethod" label="试验方法" show-overflow-tooltip min-width="180" />
        <el-table-column prop="testConditions" label="试验条件" show-overflow-tooltip min-width="180" />
        <el-table-column prop="testDemand" label="试验要求" show-overflow-tooltip min-width="150" />
        <el-table-column prop="standardId" label="所属标准" show-overflow-tooltip min-width="150" />
        <el-table-column label="操作" min-width="80" fixed="right">
          <template #default="{ $index }">
            <el-button @click.stop="form.testProjectList.splice($index, 1)" type="danger" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="handleConfirm" :loading="loading" type="primary">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <choose-dialog v-if="chooseVisible" v-model:visible="chooseVisible" :selectTable="form.testProjectList" @chooseData="chooseData" />
  </div>
</template>

<script setup>
  import { addExperiment, getExperimentDetail, putExperiment } from '@/api/experiment';
  import ChooseDialog from '@/views/components/experiment/ChooseDialog.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: null,
    addDialogVisible: {
      type: Boolean,
      default: false,
    },
  });

  const form = ref({
    testProjectList: [],
  });

  const rules = reactive({
    testSheetCode: [{ required: true, message: '请输入试验单编号', trigger: 'blur' }],
    sampleName: [{ required: true, message: '请输入样品名称', trigger: 'blur' }],
    entrustedUnit: [{ required: true, message: '请输入委托单位名称', trigger: 'blur' }],
    contactPerson: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    contactPhone: [
      { required: true, trigger: 'blur', message: '请输入联系方式' },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur',
      },
    ],
  });
  const loading = ref(false);
  const chooseVisible = ref(false);

  const getDetail = () => {
    getExperimentDetail(props.id).then(res => {
      form.value = res.data;
    });
  };

  const chooseData = data => {
    form.value.testProjectList = data;
  };

  const handleConfirm = () => {
    loading.value = true;
    if (form.value.testProjectList && form.value.testProjectList.length > 0) {
      proxy.$refs.queryRef.validate(valid => {
        if (valid) {
          if (props.id) {
            putExperiment(form.value)
              .then(() => {
                proxy.$modal.msgSuccess('更新成功！');
                emit('updateData');
                handleClose();
              })
              .finally(() => {
                loading.value = false;
              });
          } else {
            addExperiment(form.value)
              .then(() => {
                proxy.$modal.msgSuccess('保存成功！');
                emit('updateData');
                handleClose();
              })
              .finally(() => {
                loading.value = false;
              });
          }
        } else {
          loading.value = false;
        }
      });
    } else {
      loading.value = false;
      proxy.$message.warning('请添加试验项目！');
    }
  };

  const handleClose = () => {
    emit('update:addDialogVisible', false);
    proxy.$refs.queryRef.resetFields();
  };

  const emit = defineEmits(['update:addDialogVisible', 'updateData']);

  if (props.id) getDetail();
</script>

<style lang="scss" scoped></style>
