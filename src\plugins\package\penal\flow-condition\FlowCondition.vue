<template>
  <div class="panel-tab__content">
    <el-form :model="flowConditionForm" label-width="90px" @submit.native.prevent>
      <el-form-item label="流转类型">
        <el-select v-model="flowConditionForm.type" @change="updateFlowType">
          <el-option label="普通流转路径" value="normal" />
          <el-option label="默认流转路径" value="default" />
          <el-option label="条件流转路径" value="condition" />
        </el-select>
      </el-form-item>
      <el-form-item label="条件格式" v-if="flowConditionForm.type === 'condition'" key="condition">
        <el-select v-model="flowConditionForm.conditionType">
          <el-option label="表达式" value="expression" />
          <el-option label="脚本" value="script" />
        </el-select>
      </el-form-item>
      <el-form-item label="表达式" v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'expression'" key="express">
        <el-input v-model="flowConditionForm.body" clearable @change="updateFlowCondition" />
      </el-form-item>
      <template v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'script'">
        <el-form-item label="脚本语言" key="language">
          <el-input v-model="flowConditionForm.language" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item label="脚本类型" key="scriptType">
          <el-select v-model="flowConditionForm.scriptType">
            <el-option label="内联脚本" value="inlineScript" />
            <el-option label="外部脚本" value="externalScript" />
          </el-select>
        </el-form-item>
        <el-form-item label="脚本" v-if="flowConditionForm.scriptType === 'inlineScript'" key="body">
          <el-input v-model="flowConditionForm.body" type="textarea" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item label="资源地址" v-if="flowConditionForm.scriptType === 'externalScript'" key="resource">
          <el-input v-model="flowConditionForm.resource" clearable @change="updateFlowCondition" />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup name="FlowCondition">

const {proxy} = getCurrentInstance()

const props = defineProps({
  businessObject: {
    type: Object,
    default: {}
  },
  type: String
})
const {businessObject,type} = toRefs(props)
const state = reactive({
  flowConditionForm: {},
  bpmnElement: undefined,
  bpmnElementSource: undefined,
  bpmnElementSourceRef: undefined,
})
const {flowConditionForm} = toRefs(state)

watch(()=>props.businessObject,() => {
  proxy.$nextTick(() => resetFlowCondition());
},{immediate: true})

function resetFlowCondition() {
  state.bpmnElement = window.bpmnInstances.bpmnElement;
  state.bpmnElementSource = state.bpmnElement.source;
  state.bpmnElementSourceRef = state.bpmnElement.businessObject.sourceRef;
  if (state.bpmnElementSourceRef && state.bpmnElementSourceRef.default && state.bpmnElementSourceRef.default.id === state.bpmnElement.id) {
    // 默认
    state.flowConditionForm = { type: "default" };
  } else if (!state.bpmnElement.businessObject.conditionExpression) {
    // 普通
    state.flowConditionForm = { type: "normal" };
  } else {
    // 带条件
    const conditionExpression = state.bpmnElement.businessObject.conditionExpression;
    state.flowConditionForm = { ...conditionExpression, type: "condition" };
    // resource 可直接标识 是否是外部资源脚本
    if (state.flowConditionForm.resource) {
      state.flowConditionForm.conditionType = "script"
      state.flowConditionForm.scriptType = "externalScript"
      return;
    }
    if (conditionExpression.language) {
      state.flowConditionForm.conditionType = "script"
      state.flowConditionForm.scriptType = "inlineScript"
      return;
    }
    state.flowConditionForm.conditionType = "expression"
  }
}
function updateFlowType(flowType) {
  // 正常条件类
  if (flowType === "condition") {
    let flowConditionRef = window.bpmnInstances.moddle.create("bpmn:FormalExpression");
    window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
      conditionExpression: flowConditionRef
    });
    return;
  }
  // 默认路径
  if (flowType === "default") {
    window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
      conditionExpression: null
    });
    window.bpmnInstances.modeling.updateProperties(state.bpmnElementSource, {
      default: state.bpmnElement
    });
    return;
  }
  // 正常路径，如果来源节点的默认路径是当前连线时，清除父元素的默认路径配置
  if (state.bpmnElementSourceRef.default && state.bpmnElementSourceRef.default.id === state.bpmnElement.id) {
    window.bpmnInstances.modeling.updateProperties(state.bpmnElementSource, {
      default: null
    });
  }
  window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
    conditionExpression: null
  });
}
function updateFlowCondition() {
  let { conditionType, scriptType, body, resource, language } = state.flowConditionForm;
  let condition;
  if (conditionType === "expression") {
    condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body });
  } else {
    if (scriptType === "inlineScript") {
      condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body, language });
      state.flowConditionForm.resource = ''
    } else {
      state.flowConditionForm.body = ''
      condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { resource, language });
    }
  }
  window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), { conditionExpression: condition });
}
onUnmounted(() => {
  state.bpmnElement = null;
  state.bpmnElementSource = null;
  state.bpmnElementSourceRef = null;
})

</script>
