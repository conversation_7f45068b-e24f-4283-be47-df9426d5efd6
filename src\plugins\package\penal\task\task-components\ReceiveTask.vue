<template>
  <div style="margin-top: 16px">
    <el-form-item label="消息实例">
      <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: nowrap">
        <el-select v-model="bindMessageId" @change="updateTaskMessage">
          <el-option v-for="id in Object.keys(messageMap)" :value="id" :label="messageMap[id]" :key="id" />
        </el-select>
        <el-button size="small" type="primary" icon="el-icon-plus" style="margin-left: 8px" @click="openMessageModel" />
      </div>
    </el-form-item>
    <el-dialog v-model="messageModelVisible" :close-on-click-modal="false" title="创建新消息" width="400px" append-to-body destroy-on-close>
      <el-form :model="newMessageForm" size="small" label-width="90px" @submit.native.prevent>
        <el-form-item label="消息ID">
          <el-input v-model="newMessageForm.id" clearable />
        </el-form-item>
        <el-form-item label="消息名称">
          <el-input v-model="newMessageForm.name" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="small" type="primary" @click="createNewMessage">确 认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReceiveTask">
const {proxy} = getCurrentInstance()

const props = defineProps({
  id: String,
  type: String
})
const {id,type} = toRefs(props)
const state = reactive({
  bindMessageId: "",
  newMessageForm: {},
  messageMap: {},
  messageModelVisible: false,
  bpmnMessageRefsMap: undefined,
  bpmnRootElements: undefined,
  bpmnElement: undefined
})
const {bindMessageId,newMessageForm,messageMap,messageModelVisible} = toRefs(state)

watch(()=>props.id,() => {
  state.bpmnElement = window.bpmnInstances.bpmnElement;
  proxy.$nextTick(() => getBindMessage());
}, { immediate: true })

onMounted(() => {
  state.bpmnMessageRefsMap = Object.create(null);
  state.bpmnRootElements = window.bpmnInstances.modeler.getDefinitions().rootElements;
  state.bpmnRootElements
    .filter(el => el.$type === "bpmn:Message")
    .forEach(m => {
      state.bpmnMessageRefsMap[m.id] = m;
      state.messageMap.m.id = m.name
    });
  
  state.messageMap['-1'] = '无'// 添加一个空对象，保证可以取消原消息绑定
})
function getBindMessage() {
  state.bpmnElement = window.bpmnInstances.bpmnElement;
  state.bindMessageId = state.bpmnElement.businessObject?.messageRef?.id || "-1";
}
function openMessageModel() {
  state.messageModelVisible = true;
  state.newMessageForm = {};
}
function createNewMessage() {
  if (state.messageMap[state.newMessageForm.id]) {
    proxy.$message.error("该消息已存在，请修改id后重新保存");
    return;
  }
  const newMessage = window.bpmnInstances.moddle.create("bpmn:Message", state.newMessageForm);
  state.bpmnRootElements.push(newMessage);
  state.messageMap[state.newMessageForm.id] = state.newMessageForm.name
  state.bpmnMessageRefsMap[state.newMessageForm.id] = newMessage;
  state.messageModelVisible = false;
}
function updateTaskMessage(messageId) {
  if (messageId === "-1") {
    window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
      messageRef: null
    });
  } else {
    window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
      messageRef: state.bpmnMessageRefsMap[messageId]
    });
  }
}
onUnmounted(() => {
  state.bpmnElement = null;
})

</script>
