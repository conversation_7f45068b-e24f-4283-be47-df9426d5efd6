<template>
  <div class="app-container flex flex-sb">
    <el-card class="box-card">
      <template v-slot:header>
        <div class="clearfix">
          <span>个人信息</span>
        </div>
      </template>
      <div>
        <div class="text-center">
          <userAvatar :user="state.user" />
        </div>
        <ul class="list-group list-group-striped">
          <li class="list-group-item">
            <svg-icon icon-class="user" />
            登录账户
            <div class="pull-right">{{ state.user.userName }}</div>
          </li>
          <li class="list-group-item">
            <svg-icon icon-class="phone" />
            手机号码
            <div class="pull-right">{{ state.user.phonenumber }}</div>
          </li>
          <li class="list-group-item">
            <svg-icon icon-class="email" />
            用户邮箱
            <div class="pull-right">{{ state.user.email }}</div>
          </li>
          <li class="list-group-item">
            <svg-icon icon-class="tree" />
            所属部门
            <div class="pull-right" v-if="state.user.dept">{{ state.user.dept.deptName }}</div>
          </li>
          <li class="list-group-item">
            <svg-icon icon-class="peoples" />
            所属角色
            <div class="pull-right">{{ state.roleGroup }}</div>
          </li>
          <li class="list-group-item">
            <svg-icon icon-class="date" />
            创建日期
            <div class="pull-right">{{ state.user.createTime }}</div>
          </li>
        </ul>
      </div>
    </el-card>
    <el-card class="box-card-right">
      <template v-slot:header>
        <div class="clearfix">
          <span>基本资料</span>
        </div>
      </template>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本资料" name="userinfo">
          <userInfo :user="state.user" @updateData="updateData" />
        </el-tab-pane>
        <el-tab-pane label="修改密码" name="resetPwd">
          <resetPwd />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup name="Profile">
  import userAvatar from './userAvatar';
  import userInfo from './userInfo';
  import resetPwd from './resetPwd';
  import { getUserProfile } from '@/api/system/user';
  import { updateUserProfile } from '@/api/system/user';

  const { proxy } = getCurrentInstance();

  const activeTab = ref('userinfo');
  const state = reactive({
    user: {},
    roleGroup: {},
    postGroup: {},
  });

  function getUser() {
    getUserProfile().then(response => {
      state.user = response.data.user;
      state.roleGroup = response.data.roleGroup;
      state.postGroup = response.data.postGroup;
    });
  }

  const updateData = data => {
    state.user = { ...data };
    updateUserProfile(state.user).then(response => {
      proxy.$modal.msgSuccess('修改成功');
    });
  };

  getUser();
</script>
<style lang="scss" scoped>
  .list-group-item {
    .svg-icon {
      margin-right: 6px;
    }
  }

  .box-card {
    width: 400px;
  }
  .box-card-right {
    margin-left: 20px;
    flex: 1;
    min-width: 1240px;
  }
</style>
