<template>
  <div class="panel-tab__content">
    <div class="panel-tab__content--title">
      <span><i class="Menu" style="margin-right: 8px; color: #555555"></i>消息列表</span>
      <el-button size="small" type="primary" icon="Plus" @click="openModel('message')">创建新消息</el-button>
    </div>
    <el-table :data="messageList" size="small" border>
      <el-table-column type="index" label="序号" width="60px" />
      <el-table-column label="消息ID" prop="id" max-width="300px" show-overflow-tooltip />
      <el-table-column label="消息名称" prop="name" max-width="300px" show-overflow-tooltip />
    </el-table>
    <div class="panel-tab__content--title" style="padding-top: 8px; margin-top: 8px; border-top: 1px solid #eeeeee">
      <span><i class="el-icon-menu" style="margin-right: 8px; color: #555555"></i>信号列表</span>
      <el-button size="small" type="primary" icon="Plus" @click="openModel('signal')">创建新信号</el-button>
    </div>
    <el-table :data="signalList" size="small" border>
      <el-table-column type="index" label="序号" width="60px" />
      <el-table-column label="信号ID" prop="id" max-width="300px" show-overflow-tooltip />
      <el-table-column label="信号名称" prop="name" max-width="300px" show-overflow-tooltip />
    </el-table>

    <el-dialog v-model="modelVisible" :title="modelConfig.title" :close-on-click-modal="false" width="400px" append-to-body destroy-on-close>
      <el-form :model="modelObjectForm" size="small" label-width="90px" @submit.native.prevent>
        <el-form-item :label="modelConfig.idLabel">
          <el-input v-model="modelObjectForm.id" clearable />
        </el-form-item>
        <el-form-item :label="modelConfig.nameLabel">
          <el-input v-model="modelObjectForm.name" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="small" @click="modelVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="addNewObject">保 存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="SignalAndMassage">

const {proxy} = getCurrentInstance()

const state = reactive({
  signalList: [],
  messageList: [],
  modelVisible: false,
  modelType: "",
  modelObjectForm: {},
  rootElements: undefined,
  messageIdMap: undefined,
  signalIdMap: undefined
})
const {signalList,messageList,modelVisible,modelType,modelObjectForm} = toRefs(state)

const modelConfig = computed(() => {
  if (state.modelType === "message") {
    return { title: "创建消息", idLabel: "消息ID", nameLabel: "消息名称" };
  } else {
    return { title: "创建信号", idLabel: "信号ID", nameLabel: "信号名称" };
  }
})
onMounted(() => {
  initDataList()
})
function initDataList() {
  state.rootElements = window.bpmnInstances.modeler.getDefinitions().rootElements;
  state.messageIdMap = {};
  state.signalIdMap = {};
  state.messageList = [];
  state.signalList = [];
  state.rootElements.forEach(el => {
    if (el.$type === "bpmn:Message") {
      state.messageIdMap[el.id] = true;
      state.messageList.push({ ...el });
    }
    if (el.$type === "bpmn:Signal") {
      state.signalIdMap[el.id] = true;
      state.signalList.push({ ...el });
    }
  });
}
function openModel(type) {
  state.modelType = type;
  state.modelObjectForm = {};
  state.modelVisible = true;
}
function addNewObject() {
  if (state.modelType === "message") {
    if (state.messageIdMap[state.modelObjectForm.id]) {
      return proxy.$message.error("该消息已存在，请修改id后重新保存");
    }
    const messageRef = window.bpmnInstances.moddle.create("bpmn:Message", state.modelObjectForm);
    state.rootElements.push(messageRef);
  } else {
    if (state.signalIdMap[state.modelObjectForm.id]) {
      return proxy.$message.error("该信号已存在，请修改id后重新保存");
    }
    const signalRef = window.bpmnInstances.moddle.create("bpmn:Signal", state.modelObjectForm);
    state.rootElements.push(signalRef);
  }
  state.modelVisible = false;
  initDataList();
}

</script>
