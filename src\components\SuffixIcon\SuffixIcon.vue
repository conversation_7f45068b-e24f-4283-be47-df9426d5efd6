<template>
  <svg class="icon" aria-hidden="true">
    <use :xlink:href="`#${setIconClass()}`"></use>
  </svg>
</template>

<script setup>

const props = defineProps({
  fileName: String,
  fileType: {
    type: [String,Number],
    default: '1'
  } //0：文件夹，1：文件
})
const {fileName,fileType} = toRefs(props)

const data = reactive({
  cls: 'icon-wendang1'
})

const getFileExtension = () => {
  let fileExtension = ""
  if(!props.fileName) return 

  if (props.fileName.lastIndexOf(".") > -1) {
    fileExtension = props.fileName.slice(props.fileName.lastIndexOf(".") + 1)
    fileExtension = fileExtension.toLowerCase()
  }
  return fileExtension
}
const setIconClass = () => {
  if(props.fileType == '0') {
    data.cls = 'icon-wenjianleixing-biaozhuntu-wenjianjia'
    return data.cls
  }
  let fileExtension = getFileExtension()
  if(['pdf'].includes(fileExtension)){
    data.cls = 'icon-pdf'
  }else if(['doc','docx'].includes(fileExtension)){
    data.cls = 'icon-word-1'
  }else if(['xls','xlsx'].includes(fileExtension)){
    data.cls = 'icon-xlxs_xls'
  }else if(['ppt','pptx'].includes(fileExtension)){
    data.cls = 'icon-ppt_pptx'
  }else if(['jpg','jpeg','bmp','gif','png'].includes(fileExtension)){
    data.cls = 'icon-jpg_png'
  }else if(['mp4','mov','m4v','mkv'].includes(fileExtension)){
    data.cls = 'icon-mp4_avi'
  }else if(['txt'].includes(fileExtension)){
    data.cls = 'icon-txt'
  }else if(['xml'].includes(fileExtension)){
    data.cls = 'icon-xml'
  }else{
    data.cls = 'icon-wendang'
  }

  return data.cls
}
</script>

<style lang="scss" scoped>

</style>