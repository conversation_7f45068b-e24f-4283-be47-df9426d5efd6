<template>
  <div class="panel-tab__content">
    <el-form label-width="90px" @submit.native.prevent>
      <el-form-item label="ID">
        <el-input
          v-model="elementBaseInfo.id"
          :disabled="idEditDisabled || elementBaseInfo.$type === 'bpmn:Process'"
          clearable
          @change="updateBaseInfo('id')"
        />
      </el-form-item>
      <el-form-item label="名称">
        <el-input v-model="elementBaseInfo.name" clearable @change="updateBaseInfo('name')" />
      </el-form-item>
      <!--流程的基础属性-->
      <template v-if="elementBaseInfo.$type === 'bpmn:Process'">
        <el-form-item label="版本标签">
          <el-input v-model="elementBaseInfo.versionTag" clearable @change="updateBaseInfo('versionTag')" />
        </el-form-item>
        <!-- <el-form-item label="可执行">
          <el-switch v-model="elementBaseInfo.isExecutable" active-text="是" inactive-text="否" @change="updateBaseInfo('isExecutable')" />
        </el-form-item> -->
      </template>
    </el-form>
  </div>
</template>
<script setup>

const {proxy} = getCurrentInstance()

const props = defineProps({
  businessObject: Object,
  type: String,
  idEditDisabled: {
    type: Boolean,
    default: true
  }
})
const {businessObject,type,idEditDisabled} = toRefs(props)

const state = reactive({
  elementBaseInfo: {},
  bpmnElement: undefined
})
const {elementBaseInfo,bpmnElement} = toRefs(state)

watch(()=>props.businessObject,(val) => {
  if (val) {
    proxy.$nextTick(() => resetBaseInfo());
  }
})

function resetBaseInfo() {
  state.bpmnElement = window?.bpmnInstances?.bpmnElement;
  state.elementBaseInfo = JSON.parse(JSON.stringify(state.bpmnElement.businessObject));
}
function updateBaseInfo(key) {
  const attrObj = Object.create(null);
  attrObj[key] = state.elementBaseInfo[key];
  if (key === "id") {
    window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), {
      id: state.elementBaseInfo[key],
      di: { id: `${state.elementBaseInfo[key]}_di` }
    });
  } else {
    window.bpmnInstances.modeling.updateProperties(toRaw(state.bpmnElement), attrObj);
  }
}
onUnmounted(() => {
  state.bpmnElement = undefined
})

</script>
