import { getStyle } from 'element-plus/lib/utils/dom/index';

const showTip = {
  created(el, binding, vnode) {
    const { value } = binding
    const tooltipNode = vnode.children.find(childCmpt => childCmpt.component?.type.name == 'ElTooltip');
    if (tooltipNode) {
      el.addEventListener('mouseenter', () => {
        tooltipNode.component.props.disabled = true;
        const range = document.createRange();
        range.setStart(el, 0);
        range.setEnd(el, el.childNodes.length);
        const rangeWidth = Math.round(range.getBoundingClientRect().width);
        const padding = (parseInt(getStyle(el, 'paddingLeft'), 10) || 0) + (parseInt(getStyle(el, 'paddingRight'), 10) || 0);
        if(value == 'tree'){
          if (el.parentNode.offsetWidth < el.offsetWidth + 5 ) {
            tooltipNode.component.props.disabled = false;
          }
        }else{
          if (rangeWidth + padding > el.offsetWidth || el.scrollWidth > el.offsetWidth) {
            tooltipNode.component.props.disabled = false;
          }
        }        
      });
    }
  },
};
export default showTip;
