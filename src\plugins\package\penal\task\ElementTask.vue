<template>
  <div class="panel-tab__content">
    <el-form label-width="96px" @submit.native.prevent>
<!--      <el-form-item label="异步延续">-->
<!--        <el-checkbox v-model="taskConfigForm.asyncBefore" label="异步前" @change="changeTaskAsync" />-->
<!--        <el-checkbox v-model="taskConfigForm.asyncAfter" label="异步后" @change="changeTaskAsync" />-->
<!--        <el-checkbox v-model="taskConfigForm.exclusive" v-if="taskConfigForm.asyncAfter || taskConfigForm.asyncBefore" label="排除" @change="changeTaskAsync" />-->
<!--      </el-form-item>-->

      <component :is="witchTaskComponent" v-bind="$props" />
    </el-form>
  </div>
</template>

<script setup name="ElementTaskConfig">
import UserTask from "./task-components/UserTask";
import ServiceTask from "./task-components/ServiceTask";
import ScriptTask from "./task-components/ScriptTask";
import ReceiveTask from "./task-components/ReceiveTask";

const props = defineProps({
  id: String,
  type: String
})
const {id,type} = toRefs(props)

const state = reactive({
  taskConfigForm: {
    asyncAfter: false,
    asyncBefore: false,
    exclusive: false
  },
  witchTaskComponent: "",
  installedComponent: {
    // 手工任务与普通任务一致，不需要其他配置
    // 接收消息任务，需要在全局下插入新的消息实例，并在该节点下的 messageRef 属性绑定该实例
    // 发送任务、服务任务、业务规则任务共用一个相同配置
    UserTask, // 用户任务配置
    ServiceTask, // 服务任务配置
    ScriptTask, // 脚本任务配置
    ReceiveTask // 消息接收任务
  },
  bpmnElement: undefined
})
const {taskConfigForm,witchTaskComponent,installedComponent,bpmnElement} = toRefs(state)

watch(()=>props.id,() => {
  state.bpmnElement = window.bpmnInstances.bpmnElement;
  state.taskConfigForm.asyncBefore = state.bpmnElement?.businessObject?.asyncBefore;
  state.taskConfigForm.asyncAfter = state.bpmnElement?.businessObject?.asyncAfter;
  state.taskConfigForm.exclusive = state.bpmnElement?.businessObject?.exclusive;
}, { immediate: true })

watch(()=>props.type, (val) => {
  if(val){
    state.witchTaskComponent = state.installedComponent[props.type];
  }
},{ immediate: true})
function changeTaskAsync() {
  if (!state.taskConfigForm.asyncBefore && !state.taskConfigForm.asyncAfter) {
    state.taskConfigForm.exclusive = false;
  }
  window.bpmnInstances.modeling.updateProperties(window.bpmnInstances.bpmnElement, {
    ...state.taskConfigForm
  });
}

</script>
