<template>
  <svg class="icon" aria-hidden="true">
    <use :xlink:href="`#${setIconClass()}`"></use>
  </svg>
</template>

<script setup>

const props = defineProps({
  fileName: String,
  fileType: {
    type: [String,Number],
    default: '0'
  } //0：文件夹，1：文件
})
const {fileName,fileType} = toRefs(props)

const data = reactive({
  cls: 'icon-wenjianleixing-biaozhuntu-wenjianjia'
})

const getFileExtension = () => {
  let fileExtension = ""
  if(!props.fileName) return 

  if (props.fileName.lastIndexOf(".") > -1) {
    fileExtension = props.fileName.slice(props.fileName.lastIndexOf(".") + 1)
    fileExtension = fileExtension.toLowerCase()
  }
  return fileExtension
}
const setIconClass = () => {
  if(props.fileType == '0') {
    data.cls = 'icon-wenjianleixing-biaozhuntu-wenjianjia'
  }else{
    data.cls = 'icon-word-1'
  }
  
  return data.cls
}
</script>

<style lang="scss" scoped>

</style>